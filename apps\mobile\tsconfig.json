{"extends": "expo/tsconfig.base", "compilerOptions": {"strict": false, "skipLibCheck": true, "noImplicitAny": false, "jsx": "react-jsx", "esModuleInterop": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "moduleResolution": "bundler", "noEmit": true}, "include": ["**/*.ts", "**/*.tsx", "src/types/**/*.d.ts", "nativewind-env.d.ts"]}