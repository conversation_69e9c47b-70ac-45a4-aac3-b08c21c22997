#!/usr/bin/env node

/**
 * ULTIMATE FIX for Expo CLI caching bug
 * This script patches the Expo CLI to disable caching permanently
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 Applying permanent fix for Expo CLI caching bug...');

// Find the Expo CLI cache file
const cacheFilePath = path.join(__dirname, '../../node_modules/@expo/cli/build/src/api/rest/cache/wrapFetchWithCache.js');

if (fs.existsSync(cacheFilePath)) {
  try {
    let content = fs.readFileSync(cacheFilePath, 'utf8');
    
    // Replace the cache function to always return the original fetch
    const patchedContent = content.replace(
      'function wrapFetchWithCache(fetch, cache) {',
      `function wrapFetchWithCache(fetch, cache) {
    // PATCHED: Always return original fetch to avoid caching bug
    console.log('🚫 Expo cache disabled by patch');
    return fetch;
    
    // Original function below (disabled):`
    );
    
    fs.writeFileSync(cacheFilePath, patchedContent);
    console.log('✅ Successfully patched Expo CLI cache file');
    console.log('📁 Patched file:', cacheFilePath);
  } catch (error) {
    console.error('❌ Failed to patch cache file:', error.message);
  }
} else {
  console.log('⚠️ Cache file not found at:', cacheFilePath);
}

console.log('🚀 Starting Expo...');

// Set additional environment variables for Metro compatibility
process.env.NODE_OPTIONS = '--max-old-space-size=8192';
process.env.EXPO_USE_FAST_RESOLVER = 'true';

// Now start Expo normally
const { spawn } = require('child_process');
const args = process.argv.slice(2);

const expo = spawn('npx', ['expo', 'start', ...args], {
  stdio: 'inherit',
  shell: true,
  cwd: process.cwd(),
  env: process.env
});

expo.on('close', (code) => {
  process.exit(code);
});

expo.on('error', (error) => {
  console.error('❌ Failed to start Expo:', error);
  console.log('💡 If Metro transformer fails, try: npm run start-fallback');
  process.exit(1);
});
